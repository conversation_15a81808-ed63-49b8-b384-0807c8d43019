#!/usr/bin/env python3
"""
Options AI Training Agent - Machine Learning Model Development

Features:
📊 1. Multi-Model Training
- LightGBM for gradient boosting
- SGD for incremental learning
- Random Forest for baseline

📈 2. Options-Specific Features
- Greeks-based feature engineering
- Volatility surface modeling
- Time decay prediction
- Multi-timeframe analysis

⚡ 3. Advanced Training
- Hyperparameter optimization
- Cross-validation strategies
- Feature importance analysis
- Model ensemble techniques

🎯 4. Performance Optimization
- GPU acceleration support
- Parallel training pipelines
- Memory-efficient processing
- Real-time model updates

🔄 5. Incremental Learning Support
- Online learning with partial_fit
- Concept drift detection
- Model versioning and rollback
- Continuous model updates
"""

import asyncio
import logging
import polars as pl
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import json
import joblib
from dataclasses import dataclass
import warnings
import hashlib
import os
warnings.filterwarnings('ignore')

# Core ML libraries
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import SGDRegressor, PassiveAggressiveRegressor
from sklearn.tree import DecisionTreeRegressor
import optuna

logger = logging.getLogger(__name__)

class OptionsAITrainingAgent:
    """
    Options AI Training Agent for ML model development
    
    Handles:
    - Options price prediction models
    - Strategy performance prediction
    - Risk assessment models
    - Model ensemble creation
    - Incremental learning support
    """
    
    def __init__(self, config_path: str = "config/options_ai_training_config.yaml"):
        """Initialize Options AI Training Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False
        
        # Data paths
        self.data_path = Path("data")
        self.features_path = self.data_path / "features"
        self.models_path = self.data_path / "models"
        self.backtest_path = self.data_path / "results"
        self.ai_training_path = self.data_path / "ai_training"
        
        # Create directories
        self.models_path.mkdir(parents=True, exist_ok=True)
        self.ai_training_path.mkdir(parents=True, exist_ok=True)
        
        # Model storage
        self.trained_models = {}
        
        # Incremental learning support
        self.incremental_models = {}
        self.model_versions = {}
        
        # Data tracking for duplicate prevention
        self.training_registry_path = self.ai_training_path / "training_data_registry.json"
        self.training_registry = None
        
        logger.info("[INIT] Options AI Training Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent with optional parameters"""
        try:
            await self._load_config()
            
            # Store kwargs for later use
            self.init_kwargs = kwargs
            
            # Check for incremental training flag - default to True for incremental training
            self.incremental_mode = kwargs.get('incremental', True)  # Default to incremental
            
            # Load training data registry
            await self._load_training_registry()

            # Clean up problematic model files on initialization
            if self.incremental_mode:
                cleanup_count = await self.cleanup_problematic_model_files()
                if cleanup_count > 0:
                    logger.info(f"[INIT] Cleaned up {cleanup_count} problematic model files")

            logger.info(f"[SUCCESS] Options AI Training Agent initialized successfully (incremental: {self.incremental_mode})")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration with incremental training support"""
        self.config = {
            'target_variables': ['option_price', 'implied_vol', 'strategy_return', 'annualized_return', 'sharpe_ratio'],
            'feature_columns': [
                # Greeks features
                'delta', 'gamma', 'theta', 'vega', 'iv_rank',
                # Index reference features
                'index_price', 'index_return_1min', 'index_return_3min', 'index_return_5min', 'index_return_15min',
                'index_volatility', 'index_volatility_10', 'index_volatility_50',
                'index_momentum_10', 'index_momentum_20', 'index_above_sma20', 'index_above_sma50',
                # Options-specific features
                'moneyness', 'moneyness_deviation', 'distance_from_atm', 'is_itm', 'time_to_expiry',
                # Combined features
                'delta_exposure', 'delta_pnl_1min', 'gamma_risk', 'vega_exposure', 'theta_decay_rate',
                # PE/CE analysis features
                'is_pe', 'is_ce', 'direction_alignment',
                # Technical features (timeframe-dependent)
                'rsi', 'sma_20', 'ema_20', 'volume_ratio', 'momentum'
            ],
            'model_types': ['lightgbm', 'sgd'],  # Added SGD for incremental learning
            'cv_folds': 3,
            'optuna_trials': 10,
            'optuna_timeout': 60,
            'use_index_reference': True,
            'pe_ce_filter': True,
            'timeframes': ['1min', '3min', '5min', '15min'],
            'train_ensemble': True,
            'parallel_training': True,
            'max_workers': 4,
            # Incremental learning settings
            'incremental_batch_size': 1000,
            'concept_drift_threshold': 0.1,
            'model_performance_window': 100,
            'incremental_models': ['sgd', 'passive_aggressive']
        }
    
    async def _load_training_registry(self):
        """Load or create training data registry"""
        try:
            if self.training_registry_path.exists():
                with open(self.training_registry_path, 'r') as f:
                    self.training_registry = json.load(f)
                logger.info("[REGISTRY] Loaded existing training data registry")
            else:
                # Create new registry with enhanced structure
                self.training_registry = {
                    "version": "2.0",
                    "description": "Enhanced registry to track processed training data and avoid duplicates",
                    "created_at": datetime.now().isoformat(),
                    "last_updated": None,
                    "processed_data_hashes": {},
                    "training_sessions": {},
                    "data_sources": {
                        "features": {},
                        "backtest": {},
                        "strategies": {}
                    },
                    "incremental_checkpoints": {},
                    "model_performance_history": {},
                    "data_statistics": {
                        "total_records_processed": 0,
                        "unique_datasets_processed": 0,
                        "last_data_ingestion": None,
                        "data_quality_metrics": {}
                    },
                    "duplicate_prevention": {
                        "hash_algorithm": "sha256",
                        "sample_size_for_hashing": 100,
                        "duplicate_detection_enabled": True
                    }
                }
                await self._save_training_registry()
                logger.info("[REGISTRY] Created new training data registry")
        except Exception as e:
            logger.error(f"[ERROR] Failed to load training registry: {e}")
            # Create empty registry as fallback
            self.training_registry = {
                "version": "1.0",
                "processed_data_hashes": {},
                "training_sessions": {},
                "data_sources": {"features": {}, "backtest": {}, "strategies": {}},
                "incremental_checkpoints": {}
            }
    
    async def _save_training_registry(self):
        """Save training data registry"""
        try:
            self.training_registry["last_updated"] = datetime.now().isoformat()
            with open(self.training_registry_path, 'w') as f:
                json.dump(self.training_registry, f, indent=2)
            logger.info("[REGISTRY] Training data registry saved")
        except Exception as e:
            logger.error(f"[ERROR] Failed to save training registry: {e}")
    
    def _calculate_data_hash(self, data: pl.DataFrame) -> str:
        """Calculate hash of training data to detect duplicates with enhanced metadata"""
        try:
            # Get sample size from registry settings
            if self.training_registry:
                sample_size = self.training_registry.get("duplicate_prevention", {}).get("sample_size_for_hashing", 100)
            else:
                sample_size = 100

            # Create comprehensive data fingerprint
            data_info = {
                'shape': data.shape,
                'columns': sorted(data.columns),
                'dtypes': [str(dtype) for dtype in data.dtypes],
                'timestamp': datetime.now().isoformat()
            }

            # Add statistical summary for better duplicate detection
            if data.height > 0:
                # Sample data for hashing
                actual_sample_size = min(sample_size, data.height)
                sample_data = data.sample(n=actual_sample_size, seed=42)

                # Calculate statistical fingerprint
                numeric_cols = [col for col in data.columns if data[col].dtype in [pl.Float64, pl.Float32, pl.Int64, pl.Int32]]
                if numeric_cols:
                    try:
                        # Use simpler statistical summary to avoid serialization issues
                        stats_summary = {}
                        for col in numeric_cols[:5]:  # Limit to first 5 numeric columns for performance
                            try:
                                col_stats = data.select(
                                    pl.mean(col).alias("mean"),
                                    pl.std(col).alias("std"),
                                    pl.min(col).alias("min"),
                                    pl.max(col).alias("max")
                                ).to_dicts()[0]
                                
                                stats_summary[col] = {k: (v if v is not None else 0.0) for k, v in col_stats.items()}

                            except Exception as e:
                                logger.warning(f"[WARNING] Failed to calculate stats for column {col}: {e}")
                                stats_summary[col] = {'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0}
                        data_info['statistical_summary'] = stats_summary
                    except Exception as e:
                        logger.warning(f"[WARNING] Failed to calculate statistical summary: {e}")
                        data_info['statistical_summary'] = {}

                # Create sample hash
                data_info['sample_hash'] = hashlib.md5(
                    str(sample_data.to_numpy()).encode()
                ).hexdigest()

                # Add data quality metrics
                data_info['quality_metrics'] = {
                    'null_percentage': float(sum(s.null_count() for s in data) / (data.height * len(data.columns))),
                    'duplicate_rows': int(data.height - data.unique().height),
                    'memory_usage_mb': float(data.estimated_size('mb'))
                }

            # Create final hash using SHA256
            data_str = json.dumps(data_info, sort_keys=True, default=str)
            final_hash = hashlib.sha256(data_str.encode()).hexdigest()

            # Store hash metadata in registry for debugging
            if "hash_metadata" not in self.training_registry:
                self.training_registry["hash_metadata"] = {}

            self.training_registry["hash_metadata"][final_hash] = {
                "created_at": datetime.now().isoformat(),
                "data_shape": data.shape,
                "sample_size_used": actual_sample_size if data.height > 0 else 0
            }

            return final_hash

        except Exception as e:
            logger.warning(f"[WARNING] Failed to calculate data hash: {e}")
            # Create fallback hash with timestamp
            fallback_hash = hashlib.sha256(f"fallback_{datetime.now().timestamp()}_{data.shape}".encode()).hexdigest()
            return fallback_hash
    
    def _is_data_already_processed(self, data_hash: str, data_source: str) -> bool:
        """Check if data has already been processed with enhanced logging"""
        try:
            if not self.training_registry:
                return False
            processed_hashes = self.training_registry.get("processed_data_hashes", {})

            if data_hash in processed_hashes:
                existing_entry = processed_hashes[data_hash]
                logger.info(f"[DUPLICATE] Data hash {data_hash[:12]}... already processed")
                logger.info(f"[DUPLICATE] Original source: {existing_entry.get('source', 'unknown')}")
                logger.info(f"[DUPLICATE] Original processed at: {existing_entry.get('processed_at', 'unknown')}")
                return True

            return False

        except Exception as e:
            logger.error(f"[ERROR] Failed to check if data already processed: {e}")
            return False

    def _mark_data_as_processed(self, data_hash: str, data_source: str, metadata: Dict[str, Any]):
        """Mark data as processed in registry with enhanced metadata"""
        try:
            if not self.training_registry:
                self.training_registry = {}
            if "processed_data_hashes" not in self.training_registry:
                self.training_registry["processed_data_hashes"] = {}

            # Enhanced metadata for better tracking
            enhanced_metadata = {
                "source": data_source,
                "processed_at": datetime.now().isoformat(),
                "metadata": metadata,
                "hash_algorithm": "sha256",
                "registry_version": self.training_registry.get("version", "1.0")
            }

            self.training_registry["processed_data_hashes"][data_hash] = enhanced_metadata

            # Update statistics
            if "data_statistics" not in self.training_registry:
                self.training_registry["data_statistics"] = {}

            stats = self.training_registry["data_statistics"]
            stats["total_records_processed"] = stats.get("total_records_processed", 0) + metadata.get("records", 0)
            stats["unique_datasets_processed"] = len(self.training_registry["processed_data_hashes"])
            stats["last_data_ingestion"] = datetime.now().isoformat()

            logger.info(f"[REGISTRY] Marked data hash {data_hash[:12]}... as processed")
            logger.info(f"[REGISTRY] Source: {data_source}, Records: {metadata.get('records', 'unknown')}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to mark data as processed: {e}")

    def _cleanup_old_registry_entries(self, max_entries: int = 1000):
        """Clean up old registry entries to prevent unlimited growth"""
        try:
            if not self.training_registry:
                return
            processed_hashes = self.training_registry.get("processed_data_hashes", {})

            if len(processed_hashes) > max_entries:
                logger.info(f"[CLEANUP] Registry has {len(processed_hashes)} entries, cleaning up old ones...")

                # Sort by processed_at timestamp and keep only the most recent entries
                sorted_entries = sorted(
                    processed_hashes.items(),
                    key=lambda x: x[1].get("processed_at", ""),
                    reverse=True
                )

                # Keep only the most recent entries
                recent_entries = dict(sorted_entries[:max_entries])
                self.training_registry["processed_data_hashes"] = recent_entries

                removed_count = len(processed_hashes) - len(recent_entries)
                logger.info(f"[CLEANUP] Removed {removed_count} old registry entries")

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup old registry entries: {e}")
    
    async def start(self, **kwargs) -> bool:
        """Start the AI training agent with incremental learning support"""
        try:
            logger.info("[START] Starting Options AI Training Agent...")
            
            # Check if incremental mode is enabled
            incremental = kwargs.get('incremental', False) or self.incremental_mode
            
            if incremental:
                logger.info("[INCREMENTAL] Starting incremental training mode...")
                return await self._start_incremental_training(**kwargs)
            else:
                logger.info("[BATCH] Starting batch training mode...")
                return await self._start_batch_training(**kwargs)
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False
    
    async def _start_incremental_training(self, **kwargs) -> bool:
        """Start incremental training workflow with duplicate data filtering"""
        try:
            logger.info("[INCREMENTAL] Starting incremental training with duplicate filtering...")
            
            # Create training session ID
            session_id = f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Load existing models if available
            await self._load_existing_models()
            
            # Load and filter training data
            training_data = await self._load_and_filter_training_data()
            if training_data is None or training_data.height == 0:
                logger.info("[INCREMENTAL] No new data to process (all data already processed)")
                return True
            
            logger.info(f"[INCREMENTAL] Found {training_data.height} new records for training")
            
            # Record training session
            self.training_registry["training_sessions"][session_id] = {
                "started_at": datetime.now().isoformat(),
                "data_records": training_data.height,
                "status": "in_progress"
            }
            
            # Process data in batches for incremental learning
            batch_size = self.config['incremental_batch_size']
            total_rows = training_data.height
            processed_batches = 0
            
            logger.info(f"[INCREMENTAL] Processing {total_rows} records in batches of {batch_size}")
            
            for i in range(0, total_rows, batch_size):
                batch_end = min(i + batch_size, total_rows)
                batch_data = training_data.slice(i, batch_end - i)
                
                logger.info(f"[INCREMENTAL] Processing batch {i//batch_size + 1}/{(total_rows + batch_size - 1)//batch_size}")
                
                # Calculate batch hash and check if already processed
                batch_hash = self._calculate_data_hash(batch_data)
                
                if not self._is_data_already_processed(batch_hash, "incremental_batch"):
                    # Update models incrementally
                    await self._update_models_incrementally(batch_data)
                    
                    # Mark batch as processed
                    self._mark_data_as_processed(batch_hash, "incremental_batch", {
                        "session_id": session_id,
                        "batch_number": i//batch_size + 1,
                        "records": batch_data.height
                    })
                    
                    processed_batches += 1
                    
                    # Check for concept drift
                    if await self._detect_concept_drift(batch_data):
                        logger.warning("[DRIFT] Concept drift detected, retraining models...")
                        await self._retrain_models_on_drift(batch_data)
                else:
                    logger.info(f"[SKIP] Batch {i//batch_size + 1} already processed, skipping...")
            
            # Update training session
            self.training_registry["training_sessions"][session_id].update({
                "completed_at": datetime.now().isoformat(),
                "processed_batches": processed_batches,
                "status": "completed"
            })
            
            # Cleanup old registry entries to prevent unlimited growth
            self._cleanup_old_registry_entries(max_entries=1000)

            # Save updated models and registry
            await self._save_models()
            await self._save_training_registry()
            
            logger.info(f"[SUCCESS] Incremental training completed - processed {processed_batches} new batches")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Incremental training failed: {e}")
            # Update session status
            if 'session_id' in locals():
                self.training_registry["training_sessions"][session_id]["status"] = "failed"
                self.training_registry["training_sessions"][session_id]["error"] = str(e)
                await self._save_training_registry()
            return False
    
    async def _start_batch_training(self, **kwargs) -> bool:
        """Start traditional batch training workflow"""
        try:
            # Load training data
            training_data = await self._load_training_data()
            if training_data is None:
                return False
            
            # Train models for each target
            for target in self.config['target_variables']:
                if target not in training_data.columns:
                    continue
                    
                await self._train_target_models(training_data, target)
            
            # Save models
            await self._save_models()
            
            logger.info("[SUCCESS] Batch training completed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Batch training failed: {e}")
            return False
    
    async def _load_and_filter_training_data(self) -> Optional[pl.DataFrame]:
        """Load training data and filter out duplicates using registry"""
        try:
            logger.info("[LOAD] Loading and filtering training data for incremental training...")

            # Load all available training data
            all_data = await self._load_training_data()
            if all_data is None or all_data.height == 0:
                logger.warning("[WARNING] No training data found")
                return None

            # Calculate hash for the entire dataset
            data_hash = self._calculate_data_hash(all_data)

            # Check if this exact dataset has been processed before
            if self._is_data_already_processed(data_hash, "full_dataset"):
                logger.info("[FILTER] Entire dataset already processed, checking for new data...")

                # Look for newer data files that might have been added
                new_data = await self._load_new_data_since_last_training()
                if new_data is None or new_data.height == 0:
                    logger.info("[FILTER] No new data found since last training")
                    return None

                # Calculate hash for new data
                new_data_hash = self._calculate_data_hash(new_data)
                if not self._is_data_already_processed(new_data_hash, "incremental_data"):
                    logger.info(f"[FILTER] Found {new_data.height} new records for incremental training")
                    self._mark_data_as_processed(new_data_hash, "incremental_data", {
                        "records": new_data.height,
                        "source": "incremental_update"
                    })
                    return new_data
                else:
                    logger.info("[FILTER] New data already processed")
                    return None
            else:
                # This is a new dataset, mark it as processed
                logger.info(f"[FILTER] Processing new dataset with {all_data.height} records")
                self._mark_data_as_processed(data_hash, "full_dataset", {
                    "records": all_data.height,
                    "source": "full_dataset"
                })
                return all_data

        except Exception as e:
            logger.error(f"[ERROR] Failed to load and filter training data: {e}")
            return None

    async def _load_new_data_since_last_training(self) -> Optional[pl.DataFrame]:
        """Load only new data files created since last training session"""
        try:
            # Get timestamp of last training session
            last_training_time = self._get_last_training_timestamp()
            if last_training_time is None:
                # No previous training, load all data
                return await self._load_training_data()

            logger.info(f"[LOAD] Looking for data files newer than {last_training_time}")

            # Load feature data from all timeframes, filtering by modification time
            timeframes = self.config['timeframes']
            underlyings = ['NIFTY', 'BANKNIFTY']
            new_training_data = []

            for timeframe in timeframes:
                timeframe_path = self.features_path / timeframe
                if not timeframe_path.exists():
                    continue

                for underlying in underlyings:
                    feature_files = list(timeframe_path.glob(f"{underlying}_{timeframe}_features_*.parquet"))

                    # Filter files by modification time
                    new_files = [
                        f for f in feature_files
                        if f.stat().st_mtime > last_training_time.timestamp()
                    ]

                    if new_files:
                        # Load the newest file
                        latest_file = max(new_files, key=lambda x: x.stat().st_mtime)
                        df = pl.read_parquet(latest_file)

                        # Filter for PE/CE options if enabled
                        if self.config['pe_ce_filter'] and 'option_type' in df.columns:
                            df = df.filter(
                                pl.col('option_type').is_not_null() &
                                pl.col('option_type').is_in(['PE', 'CE'])
                            )

                        if df.height > 0:
                            df = df.with_columns([pl.lit(timeframe).alias('timeframe')])
                            new_training_data.append(df)
                            logger.info(f"[LOAD] Found new data: {df.height} records from {underlying} {timeframe}")

            if not new_training_data:
                logger.info("[LOAD] No new data files found")
                return None

            # Combine new data
            combined_new_data = pl.concat(new_training_data, how="diagonal")

            # Create target variables if missing
            combined_new_data = await self._create_target_variables(combined_new_data)

            logger.info(f"[SUCCESS] Loaded {combined_new_data.height} new training samples")
            return combined_new_data

        except Exception as e:
            logger.error(f"[ERROR] Failed to load new data since last training: {e}")
            return None

    def _get_last_training_timestamp(self) -> Optional[datetime]:
        """Get timestamp of last training session from registry"""
        try:
            if not self.training_registry or "training_sessions" not in self.training_registry:
                return None

            training_sessions = self.training_registry["training_sessions"]
            if not training_sessions:
                return None

            # Find the most recent completed training session
            latest_session = None
            latest_time = None

            for session_id, session_data in training_sessions.items():
                if session_data.get("status") == "completed" and "completed_at" in session_data:
                    session_time = datetime.fromisoformat(session_data["completed_at"])
                    if latest_time is None or session_time > latest_time:
                        latest_time = session_time
                        latest_session = session_data

            return latest_time

        except Exception as e:
            logger.error(f"[ERROR] Failed to get last training timestamp: {e}")
            return None

    async def _load_training_data(self) -> Optional[pl.DataFrame]:
        """Load training data from feature engineering and backtest outputs"""
        try:
            logger.info("[LOAD] Loading training data and backtest results...")

            # 1. Load latest backtest results
            backtest_files = list(self.backtest_path.glob("backtest_results_*.parquet"))
            if not backtest_files:
                logger.warning("[WARNING] No backtest result files found. Cannot proceed with training.")
                return None
            
            latest_backtest_file = max(backtest_files, key=lambda x: x.stat().st_mtime)
            backtest_df = pl.read_parquet(latest_backtest_file)
            logger.info(f"[LOAD] Loaded {backtest_df.height} records from latest backtest file: {latest_backtest_file.name}")

            # 2. Load feature data from all timeframes
            timeframes = self.config['timeframes']
            underlyings = ['NIFTY', 'BANKNIFTY']
            all_feature_data = []

            for timeframe in timeframes:
                timeframe_path = self.features_path / timeframe
                if not timeframe_path.exists():
                    continue

                for underlying in underlyings:
                    feature_files = list(timeframe_path.glob(f"{underlying}_{timeframe}_features_*.parquet"))
                    if feature_files:
                        latest_file = max(feature_files, key=lambda x: x.stat().st_mtime)
                        df = pl.read_parquet(latest_file)
                        
                        # Add underlying and timeframe, essential for joining
                        df = df.with_columns([
                            pl.lit(underlying).alias('underlying'),
                            pl.lit(timeframe).alias('timeframe')
                        ])
                        all_feature_data.append(df)

            if not all_feature_data:
                logger.warning("[WARNING] No feature data found.")
                return None

            feature_df = pl.concat(all_feature_data, how="diagonal")
            logger.info(f"[LOAD] Loaded {feature_df.height} total feature records.")

            # 3. Join feature data with backtest data
            # The strategy_id is now expected to be constructed correctly in the feature engineering agent.
            # The format should be: 'strategy_prefix_underlying_strike_price_timestamp'
            if 'strategy_id' not in feature_df.columns:
                logger.warning("[WARNING] 'strategy_id' not found in feature data. Joining may fail.")
                return None

            # Filter out rows with null strategy_id (these are index data, not options data)
            feature_df_filtered = feature_df.filter(pl.col("strategy_id").is_not_null())
            logger.info(f"[FILTER] Filtered feature data from {feature_df.height} to {feature_df_filtered.height} records (removed null strategy_ids)")

            if feature_df_filtered.height == 0:
                logger.warning("[WARNING] No feature data with valid strategy_id found after filtering.")
                return None

            # Prepare backtest_df for joining
            if 'strategy_id' not in backtest_df.columns:
                logger.warning("[WARNING] 'strategy_id' not found in backtest data. Cannot extract underlying for join.")
                return None

            backtest_df_prepared = backtest_df.with_columns([
                pl.col("strategy_id").str.split("_").list.get(0).alias("underlying"), # Extract underlying from backtest strategy_id
            ])

            # Check for 'timeframe' in backtest_df_prepared
            if 'timeframe' not in backtest_df_prepared.columns:
                logger.error("[ERROR] 'timeframe' column not found in backtest results. Cannot perform a meaningful join with feature data.")
                logger.info(f"[DEBUG] Sample backtest strategy_ids: {backtest_df.select('strategy_id').head(5).to_dicts()}")
                return None

            # Perform the join on 'underlying' and 'timeframe'
            # This is a more generic join due to strategy_id format mismatch
            combined_data = feature_df_filtered.join(
                backtest_df_prepared,
                on=["underlying", "timeframe"],
                how="inner",
                suffix="_backtest"
            )

            if combined_data.height == 0:
                logger.warning("[WARNING] Join between feature data and backtest results yielded 0 records. This might be due to strategy_id format mismatch or missing data for common underlying/timeframe combinations.")
                logger.info(f"[DEBUG] Sample feature data (underlying, timeframe): {feature_df_filtered.select('underlying', 'timeframe').unique().head(5).to_dicts()}")
                logger.info(f"[DEBUG] Sample backtest data (underlying, timeframe): {backtest_df_prepared.select('underlying', 'timeframe').unique().head(5).to_dicts()}")
                return None

            logger.info(f"[SUCCESS] Successfully joined {combined_data.height} records from features and backtest results.")
            return combined_data
        except Exception as e:
            logger.error(f"[ERROR] Failed to load and join training data: {e}")
            return None

    async def _create_target_variables(self, data: pl.DataFrame) -> pl.DataFrame:
        """
        DEPRECATED: This function is now a fallback for when backtest data cannot be joined.
        The primary source for target variables should be the backtest results.
        """
        logger.warning("[DEPRECATED] Creating target variables manually. Backtest data was not successfully joined.")
        try:
            # Create option_price target from close price
            if 'close' in data.columns and 'option_price' not in data.columns:
                data = data.with_columns([pl.col('close').alias('option_price')])

            # Create implied_vol target
            if 'implied_vol' not in data.columns:
                if 'close' in data.columns:
                    data = data.with_columns([
                        pl.col('close').pct_change().rolling_std(window_size=20).alias('implied_vol')
                    ])
                else:
                    data = data.with_columns([pl.lit(0.2).alias('implied_vol')])

            # Create strategy_return target
            if 'strategy_return' not in data.columns:
                if 'close' in data.columns:
                    data = data.with_columns([pl.col('close').pct_change().alias('strategy_return')])
                else:
                    data = data.with_columns([pl.lit(0.0).alias('strategy_return')])

            # Create annualized_return target
            if 'annualized_return' not in data.columns:
                data = data.with_columns([
                    (pl.col('strategy_return') * 252).alias('annualized_return')
                ])

            # Create sharpe_ratio target
            if 'sharpe_ratio' not in data.columns:
                data = data.with_columns([
                    (pl.col('strategy_return') / pl.col('implied_vol')).alias('sharpe_ratio')
                ])

            return data

        except Exception as e:
            logger.error(f"[ERROR] Failed to create fallback target variables: {e}")
            return data

    async def _train_target_models(self, data: pl.DataFrame, target: str):
        """Train models for specific target"""
        try:
            logger.info(f"[TRAIN] Training models for target: {target}")

            if target not in data.columns:
                logger.warning(f"[WARNING] Target {target} not found in data")
                return

            # Feature selection
            feature_cols = [col for col in self.config['feature_columns'] if col in data.columns]
            if not feature_cols:
                logger.warning(f"[WARNING] No features found for target {target}")
                return

            # Data preparation with Polars
            prepared_data = data.select(feature_cols + [target])

            # Impute missing values
            for col_name in feature_cols:
                if prepared_data[col_name].dtype in [pl.Float32, pl.Float64, pl.Int32, pl.Int64]:
                    median_val = prepared_data[col_name].median()
                    if median_val is not None:
                        prepared_data = prepared_data.with_columns(pl.col(col_name).fill_null(median_val))
                elif prepared_data[col_name].dtype == pl.Utf8:
                    mode_val = prepared_data[col_name].mode()
                    if mode_val.height > 0:
                        prepared_data = prepared_data.with_columns(pl.col(col_name).fill_null(mode_val[0]))
            
            prepared_data = prepared_data.drop_nulls()

            if prepared_data.height == 0:
                logger.warning(f"[WARNING] No valid data for target {target} after dropping nulls")
                return

            X = prepared_data.select(feature_cols).to_numpy()
            y = prepared_data.select(target).to_numpy().ravel()

            if len(X) == 0:
                logger.warning(f"[WARNING] No valid data for target {target}")
                return

            # Train models
            for model_type in self.config['model_types']:
                model_key = f"{target}_{model_type}"
                clean_model_key = self._get_clean_model_name(model_key)

                try:
                    model = None
                    if model_type == 'lightgbm':
                        model = await self._train_lightgbm(X, y, clean_model_key)
                    elif model_type == 'sgd':
                        model = await self._train_sgd(X, y, clean_model_key)
                    elif model_type == 'tabnet':
                        logger.warning(f"[WARNING] TabNet training not implemented, skipping {clean_model_key}")
                        continue
                    else:
                        logger.warning(f"[WARNING] Unknown model type '{model_type}', skipping {clean_model_key}")
                        continue

                    if model is not None:
                        self.trained_models[clean_model_key] = {
                            'model': model,
                            'target': target,
                            'model_type': model_type,
                            'features': feature_cols
                        }
                        logger.info(f"[SUCCESS] Trained {clean_model_key}")
                    else:
                        logger.warning(f"[WARNING] Model training returned None for {clean_model_key}")

                except Exception as e:
                    logger.error(f"[ERROR] Failed to train {clean_model_key}: {e}")
                    continue

        except Exception as e:
            logger.error(f"[ERROR] Failed to train models for {target}: {e}")

    async def _train_lightgbm(self, X: np.ndarray, y: np.ndarray, model_key: str):
        """Train LightGBM model with Optuna optimization"""
        try:
            def objective(trial):
                params = {
                    'objective': 'regression',
                    'metric': 'rmse',
                    'boosting_type': 'gbdt',
                    'num_leaves': trial.suggest_int('num_leaves', 10, 300),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
                    'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
                    'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                    'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                    'verbosity': -1
                }

                # Time series cross-validation
                tscv = TimeSeriesSplit(n_splits=self.config['cv_folds'])
                scores = []

                for train_idx, val_idx in tscv.split(X):
                    X_train, X_val = X[train_idx], X[val_idx]
                    y_train, y_val = y[train_idx], y[val_idx]

                    train_data = lgb.Dataset(X_train, label=y_train)
                    val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)

                    model = lgb.train(
                        params,
                        train_data,
                        valid_sets=[val_data],
                        num_boost_round=100,  # Reduced for faster training
                        callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
                    )

                    y_pred = model.predict(X_val)
                    score = mean_squared_error(y_val, y_pred)
                    scores.append(score)

                return np.mean(scores)

            # Optimize hyperparameters
            study = optuna.create_study(direction='minimize')
            study.optimize(objective, n_trials=self.config['optuna_trials'], timeout=self.config['optuna_timeout'])

            # Train final model with best parameters
            best_params = study.best_params
            best_params.update({
                'objective': 'regression',
                'metric': 'rmse',
                'verbosity': -1
            })

            train_data = lgb.Dataset(X, label=y)
            final_model = lgb.train(best_params, train_data, num_boost_round=200)

            logger.info(f"[SUCCESS] LightGBM trained for {model_key}")
            return final_model

        except Exception as e:
            logger.error(f"[ERROR] LightGBM training failed for {model_key}: {e}")
            return None

    async def _train_sgd(self, X: np.ndarray, y: np.ndarray, model_key: str):
        """Train SGD model for incremental learning"""
        try:
            # Scale features for SGD
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # Initialize SGD regressor
            model = SGDRegressor(
                loss='squared_error',
                learning_rate='adaptive',
                eta0=0.01,
                max_iter=1000,
                tol=1e-3,
                random_state=42
            )

            # Train model
            model.fit(X_scaled, y)

            # Store scaler with model
            model_with_scaler = {
                'model': model,
                'scaler': scaler
            }

            logger.info(f"[SUCCESS] SGD trained for {model_key}")
            return model_with_scaler

        except Exception as e:
            logger.error(f"[ERROR] SGD training failed for {model_key}: {e}")
            return None

    async def _load_existing_models(self):
        """Load existing models for incremental training with improved model selection"""
        try:
            model_files = list(self.models_path.glob("*.joblib"))

            if not model_files:
                logger.info("[LOAD] No existing model files found")
                return

            # Group models by base name and load the latest version of each
            model_groups = {}
            for model_file in model_files:
                if "_backup" in model_file.name:
                    continue  # Skip backup files

                base_name = self._get_clean_model_name(model_file.stem)
                
                # Check if the model type is in the config
                model_type = base_name.split('_')[-1]
                if model_type not in self.config['model_types']:
                    continue

                if base_name not in model_groups:
                    model_groups[base_name] = []
                model_groups[base_name].append(model_file)

            # Load the most recent version of each model
            for base_name, files in model_groups.items():
                try:
                    # Sort by modification time, get the newest
                    latest_file = max(files, key=lambda x: x.stat().st_mtime)
                    model_data = joblib.load(latest_file)

                    # Use clean base name as the key for consistency
                    self.trained_models[base_name] = model_data
                    logger.info(f"[LOAD] Loaded existing model: {base_name} from {latest_file.name}")

                except Exception as e:
                    logger.warning(f"[WARNING] Failed to load model group {base_name}: {e}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load existing models: {e}")

    async def _update_models_incrementally(self, batch_data: pl.DataFrame):
        """Update models incrementally with new batch data"""
        try:
            for target in self.config['target_variables']:
                if target not in batch_data.columns:
                    continue

                # Get features
                feature_cols = [col for col in self.config['feature_columns'] if col in batch_data.columns]
                if not feature_cols:
                    continue

                # Prepare data
                prepared_data = batch_data.select(feature_cols + [target]).drop_nulls()

                if prepared_data.height == 0:
                    continue

                X = prepared_data.select(feature_cols).to_numpy()
                y = prepared_data.select(target).to_numpy().ravel()

                if len(X) == 0:
                    continue

                # Update SGD models incrementally using clean model names
                sgd_key = f"{target}_sgd"
                clean_sgd_key = self._get_clean_model_name(sgd_key)

                # Try both clean and original key for backward compatibility
                model_data = None
                if clean_sgd_key in self.trained_models:
                    model_data = self.trained_models[clean_sgd_key]
                elif sgd_key in self.trained_models:
                    model_data = self.trained_models[sgd_key]
                    # Update the key to use clean name
                    self.trained_models[clean_sgd_key] = model_data
                    del self.trained_models[sgd_key]

                if model_data and isinstance(model_data, dict):
                    if 'model' in model_data and isinstance(model_data['model'], dict):
                        # Handle nested structure for SGD models
                        sgd_model = model_data['model']['model']
                        scaler = model_data['model']['scaler']
                    elif 'model' in model_data:
                        # Handle direct structure
                        sgd_model = model_data['model']
                        scaler = model_data.get('scaler')
                    else:
                        logger.warning(f"[WARNING] Unexpected model structure for {clean_sgd_key}")
                        continue

                    if scaler and hasattr(sgd_model, 'partial_fit'):
                        try:
                            # Scale new data
                            X_scaled = scaler.transform(X)

                            # Partial fit for incremental learning
                            sgd_model.partial_fit(X_scaled, y)
                            logger.info(f"[INCREMENTAL] Updated {clean_sgd_key} with {len(X)} samples")
                        except Exception as e:
                            logger.warning(f"[WARNING] Failed to update {clean_sgd_key}: {e}")
                    else:
                        logger.warning(f"[WARNING] Model {clean_sgd_key} does not support incremental learning")

        except Exception as e:
            logger.error(f"[ERROR] Failed to update models incrementally: {e}")

    async def _detect_concept_drift(self, batch_data: pl.DataFrame) -> bool:
        """Detect concept drift in new data"""
        try:
            # Simple concept drift detection based on performance degradation
            # In a real implementation, you would use more sophisticated methods
            return False  # Placeholder

        except Exception as e:
            logger.error(f"[ERROR] Failed to detect concept drift: {e}")
            return False

    async def _retrain_models_on_drift(self, batch_data: pl.DataFrame):
        """Retrain models when concept drift is detected"""
        try:
            logger.info("[DRIFT] Retraining models due to concept drift...")
            # Implement model retraining logic here

        except Exception as e:
            logger.error(f"[ERROR] Failed to retrain models on drift: {e}")

    async def _save_models(self):
        """Save trained models with proper versioning strategy"""
        try:
            if not self.trained_models:
                logger.warning("[WARNING] No trained models to save")
                return

            saved_count = 0

            # Clean up old model files first to prevent accumulation
            await self._cleanup_old_model_files()

            for model_name, model_info in self.trained_models.items():
                try:
                    # Comprehensive validation of model data
                    if model_info is None:
                        logger.warning(f"[WARNING] Model info is None for {model_name}, skipping save.")
                        continue

                    object_to_save = None

                    if isinstance(model_info, dict):
                        # Validate dictionary structure
                        if 'model' not in model_info:
                            logger.warning(f"[WARNING] Model info for {model_name} missing 'model' key, skipping save.")
                            continue

                        model_obj = model_info.get('model')
                        if model_obj is None:
                            logger.warning(f"[WARNING] Model object is None for {model_name}, skipping save.")
                            continue

                        # Additional validation for nested structures
                        if isinstance(model_obj, dict) and 'model' in model_obj:
                            if model_obj['model'] is None:
                                logger.warning(f"[WARNING] Nested model object is None for {model_name}, skipping save.")
                                continue

                        object_to_save = model_info # Save the entire dictionary

                    else:
                        # Direct model object (like lgb.Booster)
                        object_to_save = model_info

                    if object_to_save is None:
                        logger.warning(f"[WARNING] No valid object to save for {model_name}, skipping.")
                        continue

                    # Use clean model name without timestamp accumulation
                    clean_model_name = self._get_clean_model_name(model_name)

                    # For incremental training, update existing model file
                    if self.incremental_mode:
                        model_file = self.models_path / f"{clean_model_name}.joblib"
                        # Create backup of existing model before overwriting
                        if model_file.exists():
                            backup_file = self.models_path / f"{clean_model_name}_backup.joblib"
                            if backup_file.exists():
                                backup_file.unlink()  # Remove old backup
                            model_file.rename(backup_file)
                            logger.info(f"[BACKUP] Created backup: {backup_file}")
                    else:
                        # For batch training, use timestamp
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        model_file = self.models_path / f"{clean_model_name}_{timestamp}.joblib"

                    joblib.dump(object_to_save, model_file)
                    logger.info(f"[SAVE] Saved model: {model_file}")
                    saved_count += 1

                except Exception as model_error:
                    logger.error(f"[ERROR] Failed to save individual model {model_name}: {model_error}")
                    continue

            if saved_count == 0:
                logger.warning("[WARNING] No valid models were saved")
            else:
                logger.info(f"[SUCCESS] Saved {saved_count} models successfully")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save models: {e}")

    def _get_clean_model_name(self, model_name: str) -> str:
        """Extract clean model name without timestamp suffixes"""
        try:
            # Remove timestamp patterns like _20250815_123456
            import re
            # Pattern to match timestamp suffixes
            timestamp_pattern = r'_\d{8}_\d{6}'
            clean_name = re.sub(f'({timestamp_pattern})+$', '', model_name)
            return clean_name
        except Exception as e:
            logger.warning(f"[WARNING] Failed to clean model name {model_name}: {e}")
            return model_name

    async def _cleanup_old_model_files(self, keep_latest: int = 2):
        """Clean up old model files to prevent accumulation"""
        try:
            logger.info("[CLEANUP] Cleaning up old model files...")

            # Group model files by base name
            model_groups = {}
            for model_file in self.models_path.glob("*.joblib"):
                if "_backup" in model_file.name:
                    continue  # Skip backup files

                # Extract base model name
                base_name = self._get_clean_model_name(model_file.stem)
                if base_name not in model_groups:
                    model_groups[base_name] = []
                model_groups[base_name].append(model_file)

            # For each model group, keep only the latest files
            total_removed = 0
            for base_name, files in model_groups.items():
                if len(files) > keep_latest:
                    # Sort by modification time, keep the newest
                    files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                    files_to_remove = files[keep_latest:]

                    for file_to_remove in files_to_remove:
                        try:
                            file_to_remove.unlink()
                            total_removed += 1
                            logger.info(f"[CLEANUP] Removed old model file: {file_to_remove}")
                        except Exception as e:
                            logger.warning(f"[WARNING] Failed to remove {file_to_remove}: {e}")

            if total_removed > 0:
                logger.info(f"[CLEANUP] Removed {total_removed} old model files")
            else:
                logger.info("[CLEANUP] No old model files to remove")

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup old model files: {e}")

    async def cleanup_problematic_model_files(self):
        """Clean up all existing problematic model files with accumulated timestamps"""
        try:
            logger.info("[CLEANUP] Starting cleanup of problematic model files...")

            model_files = list(self.models_path.glob("*.joblib"))
            removed_count = 0

            for model_file in model_files:
                # Check if filename has multiple timestamp patterns (problematic files)
                import re
                timestamp_pattern = r'_\d{8}_\d{6}'
                matches = re.findall(timestamp_pattern, model_file.name)

                if len(matches) > 1:  # File has accumulated timestamps
                    try:
                        model_file.unlink()
                        removed_count += 1
                        logger.info(f"[CLEANUP] Removed problematic file: {model_file.name}")
                    except Exception as e:
                        logger.warning(f"[WARNING] Failed to remove {model_file}: {e}")

            logger.info(f"[CLEANUP] Removed {removed_count} problematic model files")
            return removed_count

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup problematic model files: {e}")
            return 0

    async def cleanup(self):
        """Cleanup resources"""
        try:
            self.is_running = False
            logger.info("[CLEANUP] Options AI Training Agent cleaned up")
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")


async def main():
    """Main function for testing"""
    agent = OptionsAITrainingAgent()
    try:
        await agent.initialize()
        await agent.start()
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
