#!/usr/bin/env python3
"""
Indian Options Data Loader - Specialized loader for Indian options backtesting data

This module handles loading and processing of Indian options data including:
1. NIFTY and BANKNIFTY index data
2. Options chain data (CE/PE strikes)
3. Feature-engineered data integration
4. Proper data alignment for backtesting

Optimized with:
- GPU acceleration via cuDF when available
- Parallel processing with ThreadPoolExecutor
- Numba JIT compilation for critical functions
- Efficient Polars operations
"""

import logging
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import polars as pl
import re
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing as mp
import pytz

# GPU acceleration imports
def _setup_gpu_environment():
    """Setup GPU environment with proper CUDA library paths"""
    import os

    # Add common CUDA library paths
    cuda_paths = [
        "/usr/local/lib/ollama",
        "/usr/local/cuda/lib64",
        "/usr/lib/x86_64-linux-gnu",
        "/opt/cuda/lib64"
    ]

    current_ld_path = os.environ.get('LD_LIBRARY_PATH', '')
    for path in cuda_paths:
        if os.path.exists(path) and path not in current_ld_path:
            current_ld_path = f"{path}:{current_ld_path}" if current_ld_path else path

    os.environ['LD_LIBRARY_PATH'] = current_ld_path

try:
    _setup_gpu_environment()
    import cudf
    import cupy as cp
    GPU_AVAILABLE = True
    logging.info("GPU acceleration available with cuDF and CuPy")
except (ImportError, RuntimeError) as e:
    GPU_AVAILABLE = False
    logging.info(f"GPU acceleration not available - using CPU only: {e}")

# Numba JIT imports
try:
    from numba import jit, njit
    NUMBA_AVAILABLE = True
    logging.info("Numba JIT compilation available")
except ImportError:
    NUMBA_AVAILABLE = False
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    def njit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

logger = logging.getLogger(__name__)

# Indian timezone constants
IST = pytz.timezone('Asia/Kolkata')
UTC = pytz.UTC

# Indian market hours (IST)
MARKET_OPEN_TIME = datetime.strptime("09:15", "%H:%M").time()
MARKET_CLOSE_TIME = datetime.strptime("15:30", "%H:%M").time()

def convert_utc_to_ist(dt: datetime) -> datetime:
    """Convert UTC datetime to IST"""
    if dt.tzinfo is None:
        dt = UTC.localize(dt)
    return dt.astimezone(IST)

def convert_ist_to_utc(dt: datetime) -> datetime:
    """Convert IST datetime to UTC"""
    if dt.tzinfo is None:
        dt = IST.localize(dt)
    return dt.astimezone(UTC)

def is_market_hours(dt: datetime) -> bool:
    """Check if datetime is within Indian market hours (9:15 AM - 3:30 PM IST)"""
    ist_dt = convert_utc_to_ist(dt) if dt.tzinfo == UTC else dt
    time_only = ist_dt.time()
    return MARKET_OPEN_TIME <= time_only <= MARKET_CLOSE_TIME

def get_market_hours_filter(start_date: str, end_date: str) -> tuple:
    """Get market hours filter for the given date range"""
    # Parse dates and convert to IST market hours
    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(end_date, "%Y-%m-%d")

    # Set to market open and close times in IST
    start_market = IST.localize(datetime.combine(start_dt.date(), MARKET_OPEN_TIME))
    end_market = IST.localize(datetime.combine(end_dt.date(), MARKET_CLOSE_TIME))

    # Convert to UTC for filtering
    start_utc = start_market.astimezone(UTC)
    end_utc = end_market.astimezone(UTC)

    return start_utc, end_utc

class IndianOptionsDataLoader:
    """Specialized data loader for Indian options backtesting with GPU acceleration"""

    def __init__(self):
        self.data_path = Path("data")
        self.historical_path = self.data_path / "historical"
        self.features_path = self.data_path / "features"
        self.use_gpu = GPU_AVAILABLE
        self.max_workers = min(mp.cpu_count(), 8)  # Limit threads to avoid memory issues

        # Configure Polars for optimal performance
        pl.Config.set_streaming_chunk_size(10000)
        pl.Config.set_tbl_rows(20)

        logger.info(f"Data loader initialized - GPU: {self.use_gpu}, Workers: {self.max_workers}")
        
    async def load_integrated_backtest_data(self, start_date: str, end_date: str, 
                                          timeframes: List[str] = None) -> Tuple[pl.DataFrame, Dict[str, pl.DataFrame]]:
        """
        Load integrated data for backtesting including index data and options chains
        
        Returns:
            Tuple of (historical_data, option_chains_by_underlying)
        """
        if timeframes is None:
            timeframes = ['5min']  # Use only 5min data for faster processing
            
        try:
            # Load index data (underlying prices)
            historical_data = await self._load_index_data(start_date, end_date, timeframes)
            
            # Load options chain data
            option_chains = await self._load_options_chain_data(start_date, end_date, timeframes)
            
            # Integrate with feature data if available
            feature_data = await self._load_feature_data(start_date, end_date, timeframes)
            if feature_data is not None and not feature_data.is_empty():
                historical_data = await self._integrate_feature_data(historical_data, feature_data)
            
            logger.info(f"Loaded integrated data: {historical_data.height} historical records, "
                       f"{sum(df.height for df in option_chains.values())} option records")
            
            return historical_data, option_chains
            
        except Exception as e:
            logger.error(f"Failed to load integrated backtest data: {e}")
            return pl.DataFrame(), {}
    
    async def _load_index_data(self, start_date: str, end_date: str, timeframes: List[str]) -> pl.DataFrame:
        """Load NIFTY and BANKNIFTY index data with parallel processing"""
        all_index_data = []

        # Collect all files to process
        files_to_process = []
        for timeframe in timeframes:
            timeframe_path = self.historical_path / timeframe
            if not timeframe_path.exists():
                continue

            # Look for index files
            index_files = list(timeframe_path.glob("Index_*.parquet"))
            for file in index_files:
                underlying = self._extract_underlying_from_index_file(file.name)
                if underlying:
                    files_to_process.append((file, underlying, timeframe))

        if not files_to_process:
            logger.warning("No index files found")
            return pl.DataFrame()

        # Process files in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_file = {
                executor.submit(self._load_single_index_file, file_info, start_date, end_date): file_info
                for file_info in files_to_process
            }

            for future in as_completed(future_to_file):
                try:
                    df = future.result()
                    if df is not None and not df.is_empty():
                        all_index_data.append(df)
                except Exception as e:
                    file_info = future_to_file[future]
                    logger.warning(f"Failed to load index file {file_info[0]}: {e}")

        if not all_index_data:
            logger.warning("No index data loaded")
            return pl.DataFrame()

        # Combine all index data efficiently
        combined_data = pl.concat(all_index_data, how="diagonal_relaxed")

        # Sort by timestamp using streaming for large datasets
        combined_data = combined_data.sort(['underlying', 'timestamp'])

        logger.info(f"Loaded {combined_data.height} index data records")
        return combined_data

    def _load_single_index_file(self, file_info: Tuple, start_date: str, end_date: str) -> Optional[pl.DataFrame]:
        """Load a single index file - optimized for parallel execution"""
        file, underlying, timeframe = file_info
        try:
            # Use lazy loading for better memory efficiency
            df = pl.scan_parquet(file)

            # Apply filters early to reduce memory usage
            df = df.pipe(self._apply_date_filter_lazy, start_date, end_date)

            # Collect only if data exists
            df = df.collect()

            if df.is_empty():
                return None

            # Standardize columns
            df = self._standardize_index_columns(df, underlying, timeframe)

            return df

        except Exception as e:
            logger.warning(f"Failed to load index file {file}: {e}")
            return None
    
    async def _load_options_chain_data(self, start_date: str, end_date: str,
                                     timeframes: List[str]) -> Dict[str, pl.DataFrame]:
        """Load options chain data for NIFTY and BANKNIFTY with parallel processing"""
        option_chains = {'NIFTY': [], 'BANKNIFTY': []}

        # Collect all option files to process
        files_to_process = []
        for timeframe in timeframes:
            timeframe_path = self.historical_path / timeframe
            if not timeframe_path.exists():
                continue

            # Look for option files - increase limit for better coverage
            ce_files = list(timeframe_path.glob("*_CE_*.parquet"))[:50]
            pe_files = list(timeframe_path.glob("*_PE_*.parquet"))[:50]
            option_files = ce_files + pe_files

            for file in option_files:
                option_details = self._extract_option_details(file.name)
                if option_details[0]:  # If underlying is found
                    files_to_process.append((file, option_details, timeframe))

        if not files_to_process:
            logger.warning("No option files found")
            return {}

        # Process files in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_file = {
                executor.submit(self._load_single_option_file, file_info, start_date, end_date): file_info
                for file_info in files_to_process
            }

            for future in as_completed(future_to_file):
                try:
                    result = future.result()
                    if result is not None:
                        df, actual_underlying = result
                        if actual_underlying in option_chains:
                            option_chains[actual_underlying].append(df)
                except Exception as e:
                    file_info = future_to_file[future]
                    logger.warning(f"Failed to load option file {file_info[0]}: {e}")

        # Combine data for each underlying with GPU acceleration if available
        combined_chains = {}
        for underlying, data_list in option_chains.items():
            if data_list:
                combined_df = await self._combine_option_data_gpu(data_list) if self.use_gpu else pl.concat(data_list, how="diagonal_relaxed")
                combined_df = combined_df.sort(['date', 'strike_price', 'option_type'])
                combined_chains[underlying] = combined_df
                logger.info(f"Loaded {combined_df.height} option records for {underlying}")
            else:
                logger.warning(f"No option data found for {underlying}")

        return combined_chains

    def _load_single_option_file(self, file_info: Tuple, start_date: str, end_date: str) -> Optional[Tuple[pl.DataFrame, str]]:
        """Load a single option file - optimized for parallel execution"""
        file, (filename_underlying, strike, option_type), timeframe = file_info
        try:
            # Use lazy loading for better memory efficiency
            df = pl.scan_parquet(file)

            # Apply filters early
            df = df.pipe(self._apply_date_filter_lazy, start_date, end_date)

            # Collect only if data exists
            df = df.collect()

            if df.is_empty():
                return None

            # Standardize columns
            df = self._standardize_option_columns(df, filename_underlying, strike, option_type, timeframe)

            # Get actual underlying from data
            actual_underlying = df.select('underlying').to_series().unique()[0]

            return df, actual_underlying

        except Exception as e:
            logger.warning(f"Failed to load option file {file}: {e}")
            return None
    
    async def _load_feature_data(self, start_date: str, end_date: str, 
                               timeframes: List[str]) -> Optional[pl.DataFrame]:
        """Load feature-engineered data if available"""
        all_feature_data = []
        
        for timeframe in timeframes:
            timeframe_path = self.features_path / timeframe
            if not timeframe_path.exists():
                continue
                
            feature_files = list(timeframe_path.glob("*.parquet"))
            
            for file in feature_files:
                try:
                    df = pl.read_parquet(file)
                    
                    # Add timeframe info
                    df = df.with_columns(pl.lit(timeframe).alias('timeframe'))
                    
                    # Filter by date range if timestamp column exists
                    if 'timestamp' in df.columns:
                        df = self._filter_by_date_range(df, start_date, end_date)
                    
                    if not df.is_empty():
                        all_feature_data.append(df)
                        
                except Exception as e:
                    logger.warning(f"Failed to load feature file {file}: {e}")
        
        if not all_feature_data:
            return None
            
        combined_features = pl.concat(all_feature_data, how="diagonal")
        logger.info(f"Loaded {combined_features.height} feature records")
        return combined_features
    
    def _extract_underlying_from_index_file(self, filename: str) -> Optional[str]:
        """Extract underlying from index filename"""
        match = re.search(r'Index_([A-Z]+)_', filename)
        return match.group(1) if match else None
    
    def _extract_option_details(self, filename: str) -> Tuple[Optional[str], Optional[float], Optional[str]]:
        """Extract underlying, strike, and option type from option filename"""
        # Pattern: UNDERLYING_STRIKE_TYPE_TIMEFRAME_TIMESTAMP.parquet
        match = re.search(r'([A-Z]+)_(\d+(?:\.\d+)?)_(CE|PE)_', filename)
        if match:
            underlying = match.group(1)
            strike = float(match.group(2))
            option_type = match.group(3)
            return underlying, strike, option_type
        return None, None, None
    
    def _standardize_index_columns(self, df: pl.DataFrame, underlying: str, timeframe: str) -> pl.DataFrame:
        """Standardize index data columns"""
        # Common column mappings for index data
        column_mapping = {
            'datetime': 'timestamp',
            'Date': 'timestamp',
            'Time': 'time',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'LTP': 'close',
            'ltp': 'close'
        }
        
        # Rename columns
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns:
                df = df.rename({old_col: new_col})
        
        # Add metadata
        df = df.with_columns([
            pl.lit(underlying).alias('underlying'),
            pl.lit(timeframe).alias('timeframe'),
            pl.lit('INDEX').alias('instrument_type')
        ])
        
        # Ensure timestamp column with proper timezone handling
        if 'timestamp' not in df.columns:
            if 'date' in df.columns and 'time' in df.columns:
                df = df.with_columns(
                    (pl.col('date').cast(pl.Utf8) + " " + pl.col('time').cast(pl.Utf8))
                    .str.to_datetime()
                    .alias('timestamp')
                )
            else:
                # Use current time as fallback
                df = df.with_columns(pl.lit(datetime.now(timezone.utc)).alias('timestamp'))

        # Ensure timestamp is timezone-aware (assume IST if naive, convert to UTC)
        if 'timestamp' in df.columns:
            try:
                # Check if timestamps are timezone-naive and assume they are IST
                sample_ts = df.select('timestamp').head(1).to_series()[0]
                if sample_ts and hasattr(sample_ts, 'tzinfo') and sample_ts.tzinfo is None:
                    # Convert from IST to UTC for internal storage
                    df = df.with_columns(
                        pl.col('timestamp').dt.replace_time_zone('Asia/Kolkata').dt.convert_time_zone('UTC').alias('timestamp')
                    )
            except Exception as e:
                logger.debug(f"Timezone conversion warning: {e}")
        
        # Ensure required columns exist
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            if col not in df.columns:
                if col == 'volume':
                    df = df.with_columns(pl.lit(0).alias(col))
                else:
                    # Use close price as fallback for OHLC
                    close_col = 'close' if 'close' in df.columns else 'ltp'
                    if close_col in df.columns:
                        df = df.with_columns(pl.col(close_col).alias(col))
                    else:
                        df = df.with_columns(pl.lit(0.0).alias(col))
        
        return df
    
    def _standardize_option_columns(self, df: pl.DataFrame, underlying: str, 
                                  strike: float, option_type: str, timeframe: str) -> pl.DataFrame:
        """Standardize option data columns"""
        # Common column mappings for option data
        column_mapping = {
            'datetime': 'timestamp',
            'Date': 'date',
            'Time': 'time',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'LTP': 'close',
            'ltp': 'close',
            'OI': 'open_interest',
            'OpenInterest': 'open_interest'
        }
        
        # Rename columns
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns:
                df = df.rename({old_col: new_col})
        
        # Use the underlying from the data if available, otherwise use filename
        if 'underlying' in df.columns:
            # Keep the existing underlying from data
            pass
        else:
            df = df.with_columns(pl.lit(underlying).alias('underlying'))

        # Add other metadata
        df = df.with_columns([
            pl.lit(strike).alias('strike_price'),
            pl.lit(option_type).alias('option_type'),
            pl.lit(timeframe).alias('timeframe'),
            pl.lit('OPTION').alias('instrument_type')
        ])
        
        # Create symbol using actual underlying from data
        df = df.with_columns(
            (pl.col('underlying') + "_" + pl.lit(str(int(strike))) + "_" + pl.lit(option_type)).alias('symbol')
        )
        
        # Ensure date column for options
        if 'date' not in df.columns:
            if 'timestamp' in df.columns:
                df = df.with_columns(pl.col('timestamp').dt.date().alias('date'))
            else:
                df = df.with_columns(pl.lit(datetime.now().date()).alias('date'))
        
        # Ensure required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume', 'open_interest']
        for col in required_cols:
            if col not in df.columns:
                if col in ['volume', 'open_interest']:
                    df = df.with_columns(pl.lit(0).alias(col))
                else:
                    # Use close price as fallback
                    close_col = 'close' if 'close' in df.columns else 'ltp'
                    if close_col in df.columns:
                        df = df.with_columns(pl.col(close_col).alias(col))
                    else:
                        df = df.with_columns(pl.lit(0.0).alias(col))
        
        # Add premium column (alias for close)
        df = df.with_columns(pl.col('close').alias('premium'))

        # Add DTE (Days to Expiry) - for weekly options, assume expiry is Thursday of the same week
        if 'dte' not in df.columns:
            df = df.with_columns([
                # Calculate days until next Thursday (weekly expiry)
                # For simplicity, use a fixed DTE based on weekday
                pl.when(pl.col('timestamp').dt.weekday() <= 4)  # Monday to Thursday
                .then(5 - pl.col('timestamp').dt.weekday())     # Days until Thursday
                .otherwise(7 - pl.col('timestamp').dt.weekday() + 4)  # Days until next Thursday
                .alias('dte')
            ])

        return df

    async def _combine_option_data_gpu(self, data_list: List[pl.DataFrame]) -> pl.DataFrame:
        """Combine option data using GPU acceleration if available"""
        try:
            if not self.use_gpu or not data_list:
                return pl.concat(data_list, how="diagonal_relaxed")

            # Convert to cuDF for GPU processing
            cudf_dfs = []
            for df in data_list:
                # Convert Polars to Pandas then to cuDF
                pandas_df = df.to_pandas()
                cudf_df = cudf.from_pandas(pandas_df)
                cudf_dfs.append(cudf_df)

            # Concatenate on GPU
            combined_cudf = cudf.concat(cudf_dfs, ignore_index=True)

            # Convert back to Polars
            combined_pandas = combined_cudf.to_pandas()
            combined_polars = pl.from_pandas(combined_pandas)

            logger.info("Used GPU acceleration for option data combination")
            return combined_polars

        except Exception as e:
            logger.warning(f"GPU processing failed, falling back to CPU: {e}")
            return pl.concat(data_list, how="diagonal_relaxed")

    def _apply_date_filter_lazy(self, df: pl.LazyFrame, start_date: str, end_date: str) -> pl.LazyFrame:
        """Apply date filter to lazy frame for early filtering with proper IST timezone handling"""
        try:
            # Get market hours filter in UTC
            start_utc, end_utc = get_market_hours_filter(start_date, end_date)

            # Parse dates more flexibly
            if 'T' in start_date or 'Z' in start_date:
                start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            else:
                start_dt = start_utc
                end_dt = end_utc

            # Get column names from lazy frame
            try:
                columns = df.collect_schema().names()
            except:
                # Fallback - collect a small sample to get columns
                columns = df.head(1).collect().columns

            # Try different timestamp column names
            if 'timestamp' in columns:
                try:
                    return df.filter(
                        (pl.col('timestamp') >= start_dt) &
                        (pl.col('timestamp') <= end_dt)
                    )
                except Exception:
                    # Try without timezone
                    return df.filter(
                        (pl.col('timestamp').dt.replace_time_zone(None) >= start_dt.replace(tzinfo=None)) &
                        (pl.col('timestamp').dt.replace_time_zone(None) <= end_dt.replace(tzinfo=None))
                    )
            elif 'date' in columns:
                return df.filter(
                    (pl.col('date') >= start_dt.date()) &
                    (pl.col('date') <= end_dt.date())
                )
            else:
                return df

        except Exception as e:
            logger.warning(f"Failed to apply date filter: {e}")
            return df

    def _filter_by_date_range(self, df: pl.DataFrame, start_date: str, end_date: str) -> pl.DataFrame:
        """Filter dataframe by date range with proper IST timezone handling"""
        try:
            # Get market hours filter in UTC
            start_utc, end_utc = get_market_hours_filter(start_date, end_date)
            start_dt = start_utc
            end_dt = end_utc
            
            if 'timestamp' in df.columns:
                # Ensure timestamp is datetime type
                if df['timestamp'].dtype != pl.Datetime:
                    df = df.with_columns(pl.col('timestamp').cast(pl.Datetime))
                
                # Handle timezone-aware comparisons
                try:
                    df = df.filter(
                        (pl.col('timestamp') >= start_dt) &
                        (pl.col('timestamp') <= end_dt)
                    )
                except Exception as tz_error:
                    # Try without timezone
                    logger.warning(f"Timezone comparison failed, trying without timezone: {tz_error}")
                    df = df.filter(
                        (pl.col('timestamp').dt.replace_time_zone(None) >= start_dt.replace(tzinfo=None)) &
                        (pl.col('timestamp').dt.replace_time_zone(None) <= end_dt.replace(tzinfo=None))
                    )
            elif 'date' in df.columns:
                # Filter by date column
                start_date_only = start_dt.date()
                end_date_only = end_dt.date()
                
                df = df.filter(
                    (pl.col('date') >= start_date_only) & 
                    (pl.col('date') <= end_date_only)
                )
            
            return df
            
        except Exception as e:
            logger.warning(f"Failed to filter by date range: {e}")
            return df
    
    async def _integrate_feature_data(self, historical_data: pl.DataFrame, 
                                    feature_data: pl.DataFrame) -> pl.DataFrame:
        """Integrate feature data with historical data"""
        try:
            # Try to join on common columns
            join_cols = []
            
            if 'underlying' in historical_data.columns and 'underlying' in feature_data.columns:
                join_cols.append('underlying')
            
            if 'timestamp' in historical_data.columns and 'timestamp' in feature_data.columns:
                join_cols.append('timestamp')
            elif 'date' in historical_data.columns and 'date' in feature_data.columns:
                join_cols.append('date')
            
            if join_cols:
                # Perform left join to preserve all historical data
                integrated_data = historical_data.join(
                    feature_data, 
                    on=join_cols, 
                    how='left'
                )
                logger.info(f"Integrated feature data using columns: {join_cols}")
                return integrated_data
            else:
                logger.warning("No common columns found for feature integration")
                return historical_data
                
        except Exception as e:
            logger.warning(f"Failed to integrate feature data: {e}")
            return historical_data