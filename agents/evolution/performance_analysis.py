#!/usr/bin/env python3
"""
Performance Analysis Module for Options Strategy Evolution

This module handles all performance analysis and metrics calculation for strategy evolution.
It provides comprehensive performance evaluation using real market data and backtesting results.
"""

import asyncio
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

# Define performance calculation functions directly to avoid circular imports
def calculate_sharpe_ratio_fast(returns):
    """Fast Sharpe ratio calculation"""
    if len(returns) <= 1:
        return 0.0
    mean_return = np.mean(returns)
    std_return = np.std(returns)
    return mean_return / max(std_return, 0.001)

def calculate_max_drawdown_fast(returns):
    """Fast maximum drawdown calculation"""
    cumulative = np.cumsum(returns)
    running_max = np.maximum.accumulate(cumulative)
    drawdown = cumulative - running_max
    return np.min(drawdown)

def calculate_win_rate_fast(returns):
    """Fast win rate calculation"""
    return np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0.0

def calculate_profit_factor_fast(returns):
    """Fast profit factor calculation"""
    gross_profit = np.sum(returns[returns > 0])
    gross_loss = np.abs(np.sum(returns[returns < 0]))
    return gross_profit / max(gross_loss, 0.001)

def calculate_sortino_ratio_fast(returns):
    """Fast Sortino ratio calculation"""
    if len(returns) <= 1:
        return 0.0
    mean_return = np.mean(returns)
    downside_returns = returns[returns < 0]
    downside_std = np.std(downside_returns) if len(downside_returns) > 0 else 0.001
    return mean_return / downside_std

def calculate_expectancy_fast(returns):
    """Fast expectancy calculation"""
    win_rate = calculate_win_rate_fast(returns)
    avg_win = np.mean(returns[returns > 0]) if np.any(returns > 0) else 0
    avg_loss = np.mean(returns[returns < 0]) if np.any(returns < 0) else 0
    return (win_rate * avg_win) + ((1 - win_rate) * avg_loss)

PERFORMANCE_FUNCTIONS_AVAILABLE = True

@dataclass
class PerformanceMetrics:
    """Container for strategy performance metrics"""
    strategy_id: str
    timeframe: str  # Added timeframe
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 1.0
    total_return: float = 0.0
    volatility: float = 0.0
    total_trades: int = 0
    avg_return: float = 0.0
    var_95: float = 0.0
    cvar_95: float = 0.0
    calmar_ratio: float = 0.0
    composite_score: float = 0.0
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'strategy_id': self.strategy_id,
            'timeframe': self.timeframe,  # Added timeframe to dict
            'sharpe_ratio': self.sharpe_ratio,
            'sortino_ratio': self.sortino_ratio,
            'max_drawdown': self.max_drawdown,
            'win_rate': self.win_rate,
            'profit_factor': self.profit_factor,
            'total_return': self.total_return,
            'volatility': self.volatility,
            'total_trades': self.total_trades,
            'avg_return': self.avg_return,
            'var_95': self.var_95,
            'cvar_95': self.cvar_95,
            'calmar_ratio': self.calmar_ratio,
            'composite_score': self.composite_score,
            'timestamp': self.timestamp.isoformat()
        }

class PerformanceAnalyzer:
    """Analyzes strategy performance using real market data and backtesting results"""
    
    def __init__(self, thread_pool: Optional[ThreadPoolExecutor] = None):
        self.thread_pool = thread_pool or ThreadPoolExecutor(max_workers=4)
        # Changed cache key to include timeframe
        self.performance_cache: Dict[str, PerformanceMetrics] = {}
        
    async def analyze_strategy_performance(self, strategy_config: Dict, backtest_results: Dict, timeframe: str) -> PerformanceMetrics:
        """Analyze strategy performance from backtest results"""
        try:
            strategy_id = strategy_config.get('strategy_id', 'unknown')
            cache_key = f"{strategy_id}_{timeframe}" # New cache key
            
            # Check cache first
            if cache_key in self.performance_cache:
                cached_metrics = self.performance_cache[cache_key]
                # Return cached if recent (within 1 hour)
                if (datetime.now() - cached_metrics.timestamp).seconds < 3600:
                    return cached_metrics
            
            # Extract trade data from backtest results
            # Handle both dict and BacktestResults object formats
            if hasattr(backtest_results, 'trades'):
                trades = backtest_results.trades
            else:
                trades = backtest_results.get('trades', [])

            if not trades:
                logger.warning(f"No trades found for {strategy_id} on {timeframe}. Total trades: {getattr(backtest_results, 'total_trades', 'unknown')}")
                raise ValueError(f"No trades found in backtest results for {strategy_id} on {timeframe}")
            
            # Calculate performance metrics
            metrics = await self._calculate_comprehensive_metrics(strategy_id, trades, backtest_results, timeframe) # Pass timeframe
            
            # Cache the results
            self.performance_cache[cache_key] = metrics
            
            logger.debug(f"[PERFORMANCE] Analyzed performance for {strategy_id} on {timeframe}: score={metrics.composite_score:.3f}")
            return metrics
            
        except Exception as e:
            logger.error(f"[ERROR] Performance analysis failed for {strategy_config.get('strategy_id', 'unknown')} on {timeframe}: {e}")
            # Return default metrics
            return PerformanceMetrics(strategy_id=strategy_config.get('strategy_id', 'unknown'), timeframe=timeframe) # Pass timeframe
    
    async def _calculate_comprehensive_metrics(self, strategy_id: str, trades: List[Dict], backtest_results: Dict, timeframe: str) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics from trade data"""
        try:
            # Extract returns from trades
            returns = np.array([trade.get('return', 0.0) for trade in trades if 'return' in trade])
            
            if len(returns) == 0:
                raise ValueError(f"No valid returns found for strategy {strategy_id} on {timeframe}")
            
            # Run calculations in thread pool for performance
            loop = asyncio.get_event_loop()
            metrics_data = await loop.run_in_executor(
                self.thread_pool,
                self._calculate_metrics_sync,
                returns,
                trades,
                backtest_results
            )
            
            # Create PerformanceMetrics object
            metrics = PerformanceMetrics(
                strategy_id=strategy_id,
                timeframe=timeframe, # Pass timeframe
                **metrics_data
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"[ERROR] Comprehensive metrics calculation failed: {e}")
            return PerformanceMetrics(strategy_id=strategy_id)
    
    def _calculate_metrics_sync(self, returns: np.ndarray, trades: List[Dict], backtest_results: Dict) -> Dict[str, float]:
        """Synchronous metrics calculation for thread pool execution"""
        try:
            metrics = {}
            
            # Basic metrics
            metrics['total_trades'] = len(trades)
            metrics['total_return'] = float(np.sum(returns))
            metrics['avg_return'] = float(np.mean(returns))
            metrics['volatility'] = float(np.std(returns))
            
            # Advanced metrics using imported functions if available
            if PERFORMANCE_FUNCTIONS_AVAILABLE and len(returns) > 1:
                metrics['sharpe_ratio'] = float(calculate_sharpe_ratio_fast(returns))
                metrics['max_drawdown'] = float(calculate_max_drawdown_fast(returns))
                metrics['win_rate'] = float(calculate_win_rate_fast(returns))
                metrics['profit_factor'] = float(calculate_profit_factor_fast(returns))
                metrics['sortino_ratio'] = float(calculate_sortino_ratio_fast(returns))
            else:
                # Fallback calculations
                metrics['sharpe_ratio'] = metrics['avg_return'] / max(metrics['volatility'], 0.001)
                metrics['max_drawdown'] = float(np.min(np.cumsum(returns)))
                metrics['win_rate'] = float(np.sum(returns > 0) / len(returns))
                
                gross_profit = np.sum(returns[returns > 0])
                gross_loss = np.abs(np.sum(returns[returns < 0]))
                metrics['profit_factor'] = float(gross_profit / max(gross_loss, 0.001))
                
                # Sortino ratio
                downside_returns = returns[returns < 0]
                downside_std = np.std(downside_returns) if len(downside_returns) > 0 else 0.001
                metrics['sortino_ratio'] = metrics['avg_return'] / downside_std
            
            # Risk metrics
            if len(returns) >= 20:
                metrics['var_95'] = float(np.percentile(returns, 5))
                var_95 = metrics['var_95']
                metrics['cvar_95'] = float(np.mean(returns[returns <= var_95])) if np.any(returns <= var_95) else 0.0
            else:
                metrics['var_95'] = 0.0
                metrics['cvar_95'] = 0.0
            
            # Calmar ratio
            denominator = abs(metrics['max_drawdown'])
            if denominator > 0:
                metrics['calmar_ratio'] = metrics['total_return'] / denominator
            else:
                metrics['calmar_ratio'] = 0.0
            
            # Composite performance score
            metrics['composite_score'] = self._calculate_composite_score(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"[ERROR] Sync metrics calculation failed: {e}")
            return {
                'total_trades': len(trades),
                'total_return': 0.0,
                'avg_return': 0.0,
                'volatility': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'profit_factor': 1.0,
                'sortino_ratio': 0.0,
                'var_95': 0.0,
                'cvar_95': 0.0,
                'calmar_ratio': 0.0,
                'composite_score': 0.0
            }
    
    def _calculate_composite_score(self, metrics: Dict[str, float]) -> float:
        """Calculate composite performance score"""
        try:
            # Weighted scoring system
            sharpe_score = min(max(metrics['sharpe_ratio'] * 20, -100), 100)  # Range -100 to 100
            return_score = min(metrics['total_return'] * 10, 100)  # Cap at 100
            win_rate_score = metrics['win_rate'] * 100
            drawdown_penalty = max(100 + metrics['max_drawdown'] * 10, 0)  # Penalty for drawdown
            
            composite_score = (
                sharpe_score * 0.3 +
                return_score * 0.25 +
                win_rate_score * 0.2 +
                drawdown_penalty * 0.15 +
                min(metrics['profit_factor'] * 10, 50) * 0.1  # Cap profit factor contribution
            )
            
            return float(composite_score)
            
        except Exception as e:
            logger.error(f"[ERROR] Composite score calculation failed: {e}")
            return 0.0
    
    def compare_strategies(self, metrics_list: List[PerformanceMetrics]) -> List[PerformanceMetrics]:
        """Compare and rank strategies by performance"""
        try:
            # Sort by composite score (descending)
            ranked_strategies = sorted(metrics_list, key=lambda x: x.composite_score, reverse=True)
            return ranked_strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Strategy comparison failed: {e}")
            return metrics_list
    
    def get_performance_summary(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Get human-readable performance summary"""
        try:
            return {
                'strategy_id': metrics.strategy_id,
                'overall_score': f"{metrics.composite_score:.2f}",
                'return': f"{metrics.total_return:.2%}",
                'sharpe_ratio': f"{metrics.sharpe_ratio:.2f}",
                'win_rate': f"{metrics.win_rate:.1%}",
                'max_drawdown': f"{metrics.max_drawdown:.2%}",
                'total_trades': metrics.total_trades,
                'profit_factor': f"{metrics.profit_factor:.2f}",
                'volatility': f"{metrics.volatility:.2%}"
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Performance summary failed: {e}")
            return {'strategy_id': metrics.strategy_id, 'error': str(e)}
    
    def clear_cache(self):
        """Clear performance cache"""
        self.performance_cache.clear()
        logger.info("[PERFORMANCE] Performance cache cleared")
