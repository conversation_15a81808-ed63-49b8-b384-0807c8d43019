#!/bin/bash

# GPU-Accelerated Options Trading System Runner
# This script sets up the proper environment for GPU acceleration

# Set CUDA library paths
export LD_LIBRARY_PATH="/usr/local/lib/ollama:/usr/local/cuda/lib64:/usr/lib/x86_64-linux-gnu:/opt/cuda/lib64:$LD_LIBRARY_PATH"

# Activate virtual environment
source .venv/bin/activate

# Check GPU availability
echo "🔍 Checking GPU availability..."
python -c "
import torch
print(f'PyTorch CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU: {torch.cuda.get_device_name(0)}')
    print(f'CUDA version: {torch.version.cuda}')

try:
    import cupy as cp
    print(f'CuPy available: True (version {cp.__version__})')
except:
    print('CuPy available: False')

try:
    import cudf
    print(f'cuDF available: True (version {cudf.__version__})')
except Exception as e:
    print(f'cuDF available: False ({e})')
"

echo ""
echo "🚀 Starting Options Strategy Evolution with GPU acceleration..."
echo "Command: python main.py --agent strategy_evolution $@"
echo ""

# Run the main script with all passed arguments
python main.py --agent strategy_evolution "$@"
